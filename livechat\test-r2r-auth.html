<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R2R认证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"], input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>R2R认证测试</h1>
        <p>此页面用于测试R2R服务器连接和用户认证。</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="serverUrl">R2R服务器地址:</label>
                <input type="url" id="serverUrl" value="http://192.168.0.115:7272" required>
            </div>
            
            <div class="form-group">
                <label for="email">用户邮箱:</label>
                <input type="text" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">用户密码:</label>
                <input type="password" id="password" value="change_me_immediately" required>
            </div>
            
            <button type="button" onclick="testConnection()">测试连接</button>
            <button type="button" onclick="testAuth()">测试认证</button>
            <button type="button" onclick="clearResults()">清除结果</button>
        </form>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testConnection() {
            const serverUrl = document.getElementById('serverUrl').value;
            addResult(`正在测试连接到: ${serverUrl}`, 'info');
            
            try {
                // Test basic connectivity
                const response = await fetch(`${serverUrl}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`连接成功! 服务器响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult(`连接失败! HTTP状态: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`连接错误: ${error.message}`, 'error');
            }
        }

        async function testAuth() {
            const serverUrl = document.getElementById('serverUrl').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            addResult(`正在测试认证: ${email}`, 'info');
            
            try {
                // Test authentication
                const response = await fetch(`${serverUrl}/v3/users/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        username: email,
                        password: password
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`认证成功! 响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorData = await response.text();
                    addResult(`认证失败! HTTP状态: ${response.status}\n响应: ${errorData}`, 'error');
                }
            } catch (error) {
                addResult(`认证错误: ${error.message}`, 'error');
            }
        }

        // Auto-test connection on page load
        window.onload = function() {
            addResult('页面加载完成，可以开始测试', 'info');
        };
    </script>
</body>
</html>
