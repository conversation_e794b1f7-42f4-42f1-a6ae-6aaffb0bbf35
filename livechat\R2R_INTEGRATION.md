# R2R Direct API Integration

This document describes the R2R direct API integration implemented in the livechat frontend.

## Overview

The livechat application now supports direct R2R API integration for R2R agent conversation mode. When the R2R agent is selected, the frontend bypasses the intermediate backend services and communicates directly with R2R APIs.

## Features

- **Direct R2R API Integration**: Bypasses intermediate backend services when R2R agent mode is selected
- **Automatic Agent Detection**: Automatically detects when R2R agent is selected and routes accordingly
- **Streaming Support**: Maintains real-time streaming conversation experience
- **Configuration Management**: Uses configuration files for R2R server settings and search parameters
- **Backward Compatibility**: Other conversation modes remain unchanged

## Architecture

### Key Components

1. **R2R Client Service** (`lib/services/r2rClient.ts`)
   - Handles direct R2R API communication
   - Manages authentication and conversation management
   - Provides streaming response handling

2. **R2R Service Manager** (`lib/services/r2rService.ts`)
   - Manages R2R client instances
   - Converts R2R responses to livechat format
   - Handles conversation state management

3. **R2R Chat Hook** (`lib/hooks/useR2RChat.ts`)
   - React hook for R2R direct API integration
   - Manages R2R service lifecycle
   - Handles TTS integration for R2R responses

4. **Configuration Hook** (`lib/hooks/useConfig.ts`)
   - Manages chat configuration
   - Provides R2R-specific settings
   - Handles credential management

5. **Agent Detection** (`app/(products)/sentio/hooks/chat.ts`)
   - Detects R2R agent mode
   - Routes conversations to appropriate handler
   - Maintains backward compatibility

## Configuration

### R2R Server Settings

Configure R2R server settings in `public/config.json`:

```json
{
  "server": {
    "apiUrl": "http://localhost:7272",
    "useHttps": false,
    "apiVersion": "v3",
    "timeout": 30000
  },
  "vectorSearch": {
    "enabled": true,
    "searchLimit": 10,
    "searchFilters": "{}",
    "indexMeasure": "cosine_distance"
  },
  "hybridSearch": {
    "enabled": false
  },
  "ragGeneration": {
    "temperature": 0.1,
    "topP": 1.0,
    "maxTokensToSample": 1024
  }
}
```

### Agent Settings

Configure R2R agent settings in the agent configuration:

- **base_url**: R2R server URL (例如: http://*************:7272)
- **api_key**: R2R API key (可选，如果使用API key认证)
- **search_limit**: Number of search results
- **use_hybrid_search**: Enable hybrid search
- **use_vector_search**: Enable vector search
- **temperature**: Generation temperature

**重要提示**:
- R2R集成现在使用当前登录用户的凭据
- 确保当前登录的livechat用户在R2R服务器上已注册
- 不需要单独配置email和password，系统会自动使用当前登录用户的访问令牌
- 确保R2R服务器地址可以访问

## Usage

### Selecting R2R Agent Mode

1. **确保已登录livechat**: 首先确保您已经使用R2R用户账户登录了livechat应用
2. Open the livechat application
3. Go to Settings → Engine → Agent
4. Select "R2R" from the engine dropdown
5. Configure R2R settings:
   - **base_url**: 输入R2R服务器地址 (例如: http://*************:7272)
   - 其他参数可保持默认值
6. 点击保存设置
7. Start chatting - conversations will use direct R2R API integration

**认证说明**:
- R2R集成会自动使用当前登录用户的访问令牌进行认证
- 不需要单独输入email和password
- 系统会自动检测当前登录用户并使用相应的R2R凭据

**故障排除**:
- 如果出现"访问令牌无效"错误，请重新登录livechat应用
- 如果出现"用户不存在"错误，请确保当前登录的用户在R2R服务器上已注册
- 如果出现连接错误，请检查R2R服务器地址和网络连接

### Agent Detection

The system automatically detects R2R agent mode when:
- Agent engine name contains "r2r" (case-insensitive)
- Agent engine name equals "R2R"
- Agent engine name starts with "R2R"

### Conversation Flow

When R2R agent mode is active:

1. **Message Sending**: User message is sent directly to R2R API
2. **Conversation Management**: R2R handles conversation context and history
3. **Response Streaming**: R2R responses are streamed in real-time
4. **TTS Integration**: Text-to-speech works with R2R responses
5. **Live2D Integration**: Digital human responds to R2R conversations

## Testing

### Browser Console Testing

Open browser console and run:

```javascript
testR2RIntegration()
```

This will run integration tests to verify:
- R2R agent detection
- Service creation
- Configuration integration
- Server health (if available)

### R2R认证测试页面

使用专门的测试页面验证R2R连接和认证：

1. 确保已登录livechat应用
2. 打开 `livechat/test-r2r-auth.html` 文件
3. 页面会自动加载当前用户的凭据
4. 点击"测试连接"验证服务器连接
5. 点击"测试认证"验证访问令牌
6. 查看测试结果，确保连接和认证都成功

这个测试页面可以帮助您：
- 验证R2R服务器是否可访问
- 测试当前用户的访问令牌是否有效
- 排查连接和认证问题
- 确认当前登录用户在R2R服务器上的状态

### Manual Testing

1. **Setup R2R Server**: Ensure R2R server is running on configured URL
2. **Select R2R Agent**: Choose R2R agent in settings
3. **Send Message**: Send a test message
4. **Verify Response**: Check that response comes from R2R API
5. **Check Logs**: Monitor browser console for R2R-specific logs

## Troubleshooting

### Common Issues

1. **R2R Service Not Ready**
   - Check R2R server is running
   - Verify server URL in configuration
   - Check authentication credentials

2. **Authentication Failed**
   - Verify email/password in agent settings
   - Check API key if required
   - Ensure R2R server accepts credentials

3. **No Response from R2R**
   - Check network connectivity
   - Verify R2R server health
   - Check browser console for errors

4. **Streaming Issues**
   - Verify R2R server supports streaming
   - Check for network timeouts
   - Monitor browser console for stream errors

### Debug Information

Enable debug logging by setting:
```javascript
localStorage.setItem('debug', 'r2r:*')
```

This will show detailed R2R integration logs in the browser console.

## Development

### Adding New R2R Features

1. **Extend R2R Client**: Add new methods to `r2rClient.ts`
2. **Update Service**: Modify `r2rService.ts` to handle new features
3. **Update Hook**: Extend `useR2RChat.ts` for new functionality
4. **Test Integration**: Add tests to `r2rIntegrationTest.ts`

### Configuration Changes

1. **Update Types**: Modify `types.ts` for new config options
2. **Update Defaults**: Modify `chatConfig.ts` for new defaults
3. **Update Hook**: Extend `useConfig.ts` for new settings
4. **Test Changes**: Verify configuration integration

## Dependencies

- **r2r-js**: JavaScript client library for R2R API
- **Next.js**: React framework
- **Zustand**: State management
- **TypeScript**: Type safety

## Compatibility

- **Node.js**: 18+
- **React**: 18+
- **R2R Server**: v0.4.43+
- **Browsers**: Modern browsers with ES2020 support
