# R2R认证更新说明

## 更新概述

我们已经成功更新了livechat的R2R集成，现在使用当前登录用户的访问令牌进行认证，而不是单独配置email和password。

## 主要更改

### 1. Agent配置更新 (`livebackend/configs/agents/r2rAgent.yaml`)
- **移除了** `email` 和 `password` 参数
- 现在只需要配置 `base_url` 和其他搜索参数
- 系统会自动使用当前登录用户的凭据

### 2. 配置Hook更新 (`livechat/lib/hooks/useConfig.ts`)
- 添加了 `useUserContext` 导入
- 修改了 `getR2RCredentials()` 函数：
  - 使用 `authState.email` 获取当前用户邮箱
  - 使用 `localStorage.getItem('livechatAccessToken')` 获取访问令牌
  - 返回格式：`{ email, accessToken, apiKey }`

### 3. R2R客户端服务更新 (`livechat/lib/services/r2rClient.ts`)
- 修改了 `initialize()` 方法参数：
  - 从 `{ email: string; password: string }` 
  - 改为 `{ email: string; accessToken: string; apiKey?: string }`
- 使用 `client.setTokens(accessToken, '')` 设置访问令牌
- 通过调用 `client.users.me()` 验证令牌有效性
- 改进了错误处理，提供更清晰的中文错误信息

### 4. 测试工具更新

#### 测试页面 (`livechat/test-r2r-auth.html`)
- 添加了 `loadCredentials()` 函数自动加载当前用户凭据
- 修改认证测试使用访问令牌而不是密码
- 页面加载时自动加载凭据
- 使用 `/v3/users/me` 端点验证访问令牌

#### 集成测试 (`livechat/lib/test/r2rIntegrationTest.ts`)
- 添加了 `testAccessTokenValidation()` 函数
- 更新了服务创建测试使用访问令牌格式
- 更新了主测试运行器包含访问令牌验证

### 5. 文档更新 (`livechat/R2R_INTEGRATION.md`)
- 更新了配置说明，移除了email/password配置步骤
- 添加了认证说明，说明系统自动使用当前登录用户凭据
- 更新了故障排除指南
- 更新了测试页面使用说明

## 使用方法

### 前提条件
1. 确保当前登录的livechat用户在R2R服务器上已注册
2. 确保R2R服务器地址正确配置

### 配置步骤
1. 登录livechat应用（使用R2R用户账户）
2. 进入设置 → Engine → Agent
3. 选择 "R2R" 智能体
4. 配置 `base_url` 为R2R服务器地址
5. 其他参数保持默认值
6. 保存设置
7. 开始对话

### 测试验证
1. 打开 `livechat/test-r2r-auth.html`
2. 页面会自动加载当前用户凭据
3. 点击"测试连接"验证服务器连接
4. 点击"测试认证"验证访问令牌
5. 确保所有测试通过

## 优势

1. **简化配置**: 不需要单独输入email和password
2. **安全性提升**: 使用访问令牌而不是明文密码
3. **自动同步**: 自动使用当前登录用户的身份
4. **更好的用户体验**: 减少配置步骤，降低出错可能性
5. **统一认证**: 与livechat的认证系统完全集成

## 故障排除

### 常见问题
1. **"访问令牌无效"错误**
   - 解决方案：重新登录livechat应用

2. **"用户不存在"错误**
   - 解决方案：确保当前登录用户在R2R服务器上已注册

3. **连接错误**
   - 解决方案：检查R2R服务器地址和网络连接

### 调试工具
- 使用测试页面验证连接和认证
- 检查浏览器控制台的详细错误信息
- 运行集成测试：`runR2RIntegrationTests()`

## 向后兼容性

此更新完全向后兼容，不会影响其他对话模式的功能。只有选择R2R智能体时才会使用新的认证方式。
