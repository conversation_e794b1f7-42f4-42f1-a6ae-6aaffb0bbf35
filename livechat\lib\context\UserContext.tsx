'use client';

import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from 'react';
import { r2rClient } from 'r2r-js';
import { AuthState, LoginResult, Pipeline } from '../types';

interface UserContextProps {
  pipeline: Pipeline | null;
  setPipeline: (pipeline: Pipeline | null) => void;
  selectedModel: string;
  setSelectedModel: (model: string) => void;
  isAuthenticated: boolean;
  login: (email: string, password: string, instanceUrl: string) => Promise<LoginResult>;
  loginWithToken: (token: string, instanceUrl: string) => Promise<LoginResult>;
  logout: () => Promise<void>;
  unsetCredentials: () => Promise<void>;
  register: (email: string, password: string, instanceUrl: string) => Promise<void>;
  authState: AuthState;
  getClient: () => r2rClient | null;
  client: r2rClient | null;
  viewMode: 'admin' | 'user';
  setViewMode: (mode: 'admin' | 'user') => void;
  isSuperUser: () => boolean;
  createUser: (email: string, password: string, isAdmin?: boolean) => Promise<void>;
}

const UserContext = createContext<UserContextProps>({
  pipeline: null,
  setPipeline: () => {},
  selectedModel: "null",
  setSelectedModel: () => {},
  isAuthenticated: false,
  login: async () => ({ success: false, userRole: "user" }),
  loginWithToken: async () => ({ success: false, userRole: "user" }),
  logout: async () => {},
  unsetCredentials: async () => {},
  register: async () => {},
  authState: {
    isAuthenticated: false,
    email: null,
    userRole: null,
    userId: null,
  },
  getClient: () => null,
  client: null,
  viewMode: "user", // Default to user mode for standalone chat
  setViewMode: () => {},
  isSuperUser: () => false,
  createUser: async () => {
    throw new Error("createUser is not implemented in the default context");
  },
});

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [client, setClient] = useState<r2rClient | null>(null);
  const [pipeline, setPipeline] = useState<Pipeline | null>(null);
  const [selectedModel, setSelectedModel] = useState<string>("null");
  const [viewMode, setViewMode] = useState<'admin' | 'user'>('user');
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    email: null,
    userRole: null,
    userId: null,
  });

  const isSuperUser = useCallback(() => {
    return authState.userRole === 'admin' && viewMode === 'admin';
  }, [authState.userRole, viewMode]);

  const [lastLoginTime, setLastLoginTime] = useState<number | null>(null);

  const login = useCallback(
    async (
      email: string,
      password: string,
      instanceUrl: string,
    ): Promise<LoginResult> => {
      const newClient = new r2rClient(instanceUrl);
      try {
        const tokens = await newClient.users.login({
          email: email,
          password: password,
        });

        // Handle both camelCase and snake_case response formats
        const results = tokens.results as any;
        const accessToken = results.accessToken?.token || results.access_token?.token;
        const refreshToken = results.refreshToken?.token || results.refresh_token?.token;

        if (!accessToken) {
          throw new Error('No access token received from server');
        }

        localStorage.setItem("livechatAccessToken", accessToken);
        if (refreshToken) {
          localStorage.setItem("livechatRefreshToken", refreshToken);
        }

        newClient.setTokens(accessToken, refreshToken || "");
        setClient(newClient);

        // Get user info
        const userInfo = await newClient.users.me();

        if (!userInfo.results) {
          throw new Error('Failed to get user information');
        }

        let userRole: "admin" | "user" = "user";
        try {
          await newClient.system.settings();
          userRole = "admin";
        } catch (error) {
          if (
            error instanceof Error &&
            "status" in error &&
            (error as any).status === 403
          ) {
            // User doesn't have admin access
          } else {
            console.error("Unexpected error when checking user role:", error);
          }
        }

        setAuthState({
          isAuthenticated: true,
          email: userInfo.results.email,
          userRole: userRole,
          userId: userInfo.results.id,
        });

        setLastLoginTime(Date.now());

        return { success: true, userRole };
      } catch (error) {
        console.error('Login error:', error);
        throw error;
      }
    },
    [],
  );

  const loginWithToken = useCallback(
    async (
      token: string,
      instanceUrl: string,
    ): Promise<LoginResult> => {
      const newClient = new r2rClient(instanceUrl);
      try {
        // First, set the token on the client to authenticate the request
        newClient.setTokens(token, "");

        // Try to get user info to validate the token
        const userInfo = await newClient.users.me();

        if (!userInfo.results) {
          throw new Error('Failed to get user information');
        }

        // If we get here, the token is valid, so keep it
        localStorage.setItem("livechatAccessToken", token);
        setClient(newClient);

        let userRole: "admin" | "user" = "user";
        try {
          await newClient.system.settings();
          userRole = "admin";
        } catch (error) {
          if (
            error instanceof Error &&
            "status" in error &&
            (error as any).status === 403
          ) {
            // User doesn't have admin access
          } else {
            console.error("Unexpected error when checking user role:", error);
          }
        }

        setAuthState({
          isAuthenticated: true,
          email: userInfo.results.email,
          userRole: userRole,
          userId: userInfo.results.id,
        });

        return { success: true, userRole };
      } catch (error) {
        console.error('Token login error:', error);
        // Clear invalid token
        localStorage.removeItem("livechatAccessToken");
        localStorage.removeItem("livechatRefreshToken");
        throw error;
      }
    },
    [],
  );

  const logout = useCallback(async () => {
    try {
      if (client) {
        await client.users.logout();
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem("livechatAccessToken");
      localStorage.removeItem("livechatRefreshToken");
      setClient(null);
      setAuthState({
        isAuthenticated: false,
        email: null,
        userRole: null,
        userId: null,
      });
      setLastLoginTime(null);
    }
  }, [client]);

  const unsetCredentials = useCallback(async () => {
    localStorage.removeItem("livechatAccessToken");
    localStorage.removeItem("livechatRefreshToken");
    setClient(null);
    setAuthState({
      isAuthenticated: false,
      email: null,
      userRole: null,
      userId: null,
    });
    setLastLoginTime(null);
  }, []);

  const register = useCallback(
    async (email: string, password: string, instanceUrl: string) => {
      const newClient = new r2rClient(instanceUrl);
      try {
        await newClient.users.create({
          email: email,
          password: password,
        });

        // After successful registration, log in
        await login(email, password, instanceUrl);
      } catch (error) {
        console.error('Registration error:', error);
        throw error;
      }
    },
    [login],
  );

  const getClient = useCallback(() => {
    return client;
  }, [client]);

  const createUser = useCallback(
    async (email: string, password: string, isAdmin: boolean = false) => {
      if (!client) {
        throw new Error('No authenticated client available');
      }

      try {
        await client.users.create({
          email: email,
          password: password,
        });
      } catch (error) {
        console.error('Create user error:', error);
        throw error;
      }
    },
    [client],
  );

  // Initialize authentication on mount
  useEffect(() => {
    const initializeAuth = async () => {
      const accessToken = localStorage.getItem('livechatAccessToken');
      if (accessToken) {
        try {
          const { loadChatConfig, getDeploymentUrl } = await import('../config/chatConfig');
          const config = await loadChatConfig();
          const deploymentUrl = getDeploymentUrl(config);
          await loginWithToken(accessToken, deploymentUrl);
        } catch (error) {
          console.error('Auto-login failed:', error);
          localStorage.removeItem('livechatAccessToken');
          localStorage.removeItem('livechatRefreshToken');
        }
      }
    };

    initializeAuth();
  }, [loginWithToken]);

  return (
    <UserContext.Provider
      value={{
        pipeline,
        setPipeline,
        selectedModel,
        setSelectedModel,
        isAuthenticated: authState.isAuthenticated,
        login,
        loginWithToken,
        logout,
        unsetCredentials,
        register,
        authState,
        getClient,
        client,
        viewMode,
        setViewMode,
        isSuperUser,
        createUser,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUserContext = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUserContext must be used within a UserProvider');
  }
  return context;
};
