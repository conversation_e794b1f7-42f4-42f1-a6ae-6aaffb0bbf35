/**
 * R2R Client Service for Direct API Integration
 * Handles direct communication with R2R APIs for conversation management
 */

import { r2rClient } from 'r2r-js';
import { ChatConfig } from '../types';

export interface R2RMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  sources?: any;
}

export interface R2RConversation {
  id: string;
  title?: string;
  messages: R2RMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface R2RSearchSettings {
  useHybridSearch?: boolean;
  useSemanticSearch?: boolean;
  filters?: Record<string, any>;
  limit?: number;
  chunkSettings?: {
    indexMeasure?: string;
    enabled?: boolean;
  };
  graphSettings?: {
    enabled?: boolean;
  };
}

export interface R2RGenerationConfig {
  stream?: boolean;
  temperature?: number;
  topP?: number;
  maxTokensToSample?: number;
}

export interface R2RAgentParams {
  message: R2RMessage;
  ragGenerationConfig?: R2RGenerationConfig;
  searchSettings?: R2RSearchSettings;
  conversationId?: string;
}

/**
 * R2R Client Service for direct API integration
 */
export class R2RClientService {
  private client: any;
  private config: ChatConfig;
  private isAuthenticated: boolean = false;

  constructor(config: ChatConfig) {
    this.config = config;
    this.client = new r2rClient(config.server.apiUrl);
  }

  /**
   * Initialize the R2R client with authentication
   */
  async initialize(credentials?: { email: string; password: string }): Promise<void> {
    try {
      if (credentials) {
        console.log(`正在使用凭据认证R2R服务器: ${credentials.email}`);
        await this.client.users.login(credentials.email, credentials.password);
        this.isAuthenticated = true;
        console.log('R2R客户端认证成功');
      }
    } catch (error) {
      console.error('R2R认证失败:', error);

      // Provide helpful error messages in Chinese
      let errorMessage = 'R2R认证失败';

      if (error.message) {
        if (error.message.includes('User not found') || error.message.includes('404')) {
          errorMessage = `用户不存在: ${credentials?.email}。请确保在R2R服务器上已注册该用户，或检查邮箱地址是否正确。`;
        } else if (error.message.includes('Incorrect email or password') || error.message.includes('401')) {
          errorMessage = `认证失败: 邮箱或密码错误。请检查R2R用户凭据。`;
        } else if (error.message.includes('Connection') || error.message.includes('ECONNREFUSED')) {
          errorMessage = `无法连接到R2R服务器。请检查服务器地址: ${this.config.server.apiUrl}`;
        } else {
          errorMessage = `R2R认证失败: ${error.message}`;
        }
      }

      throw new Error(errorMessage);
    }
  }

  /**
   * Check if the client is authenticated
   */
  isClientAuthenticated(): boolean {
    return this.isAuthenticated;
  }

  /**
   * Create a new conversation
   */
  async createConversation(): Promise<string> {
    try {
      const response = await this.client.conversations.create();
      return response.results.id;
    } catch (error) {
      console.error('Failed to create conversation:', error);
      throw error;
    }
  }

  /**
   * Send a message using R2R agent with streaming
   */
  async sendMessage(params: R2RAgentParams): Promise<ReadableStream> {
    try {
      // Prepare search settings based on config
      const searchSettings: R2RSearchSettings = {
        useHybridSearch: this.config.hybridSearch.enabled,
        useSemanticSearch: this.config.vectorSearch.enabled,
        filters: JSON.parse(this.config.vectorSearch.searchFilters || '{}'),
        limit: this.config.vectorSearch.searchLimit,
        chunkSettings: {
          indexMeasure: this.config.vectorSearch.indexMeasure,
          enabled: this.config.vectorSearch.enabled,
        },
        graphSettings: {
          enabled: this.config.graphSearch.enabled,
        },
        ...params.searchSettings,
      };

      // Prepare generation config
      const ragGenerationConfig: R2RGenerationConfig = {
        stream: true,
        temperature: this.config.ragGeneration.temperature,
        topP: this.config.ragGeneration.topP,
        maxTokensToSample: this.config.ragGeneration.maxTokensToSample,
        ...params.ragGenerationConfig,
      };

      // Call R2R agent API
      const response = await this.client.retrieval.agent({
        message: params.message,
        ragGenerationConfig,
        searchSettings,
        conversationId: params.conversationId,
      });

      return response;
    } catch (error) {
      console.error('Failed to send message to R2R:', error);
      throw error;
    }
  }

  /**
   * Retrieve conversation history
   */
  async getConversation(conversationId: string): Promise<R2RConversation | null> {
    try {
      const response = await this.client.conversations.retrieve({
        id: conversationId,
      });

      const messages: R2RMessage[] = response.results.map((message: any) => ({
        id: message.id,
        role: message.metadata?.role || 'user',
        content: message.metadata?.content || '',
        timestamp: message.metadata?.timestamp || Date.now(),
        sources: message.metadata?.sources || {},
      }));

      return {
        id: conversationId,
        title: response.title || 'Conversation',
        messages,
        createdAt: new Date(response.created_at || Date.now()),
        updatedAt: new Date(response.updated_at || Date.now()),
      };
    } catch (error) {
      console.error('Failed to retrieve conversation:', error);
      return null;
    }
  }

  /**
   * List all conversations
   */
  async listConversations(): Promise<R2RConversation[]> {
    try {
      const response = await this.client.conversations.list();
      return response.results.map((conv: any) => ({
        id: conv.id,
        title: conv.title || 'Conversation',
        messages: [], // Messages are loaded separately
        createdAt: new Date(conv.created_at || Date.now()),
        updatedAt: new Date(conv.updated_at || Date.now()),
      }));
    } catch (error) {
      console.error('Failed to list conversations:', error);
      return [];
    }
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(conversationId: string): Promise<boolean> {
    try {
      await this.client.conversations.delete({ id: conversationId });
      return true;
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      return false;
    }
  }

  /**
   * Update the configuration
   */
  updateConfig(config: ChatConfig): void {
    this.config = config;
    // Recreate client if server URL changed
    if (this.client.baseUrl !== config.server.apiUrl) {
      this.client = new r2rClient(config.server.apiUrl);
      this.isAuthenticated = false;
    }
  }
}

/**
 * Create a new R2R client service instance
 */
export const createR2RClientService = (config: ChatConfig): R2RClientService => {
  return new R2RClientService(config);
};

/**
 * Check R2R server health
 */
export const checkR2RServerHealth = async (serverUrl: string): Promise<boolean> => {
  try {
    const client = new r2rClient(serverUrl);
    // Try to get system status or health endpoint
    await client.system.health();
    return true;
  } catch (error) {
    console.error('R2R server health check failed:', error);
    return false;
  }
};
