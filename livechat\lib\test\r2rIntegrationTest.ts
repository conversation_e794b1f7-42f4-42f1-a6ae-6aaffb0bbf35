/**
 * R2R Integration Test
 * Simple test to verify R2R direct API integration works correctly
 */

import { isR2RAgent } from '../hooks/useR2RChat';
import { createR2RClientService, checkR2RServerHealth } from '../services/r2rClient';
import { createR2RService } from '../services/r2rService';
import { defaultChatConfig } from '../config/chatConfig';

/**
 * Test R2R agent detection
 */
export function testR2RAgentDetection(): boolean {
  console.log('Testing R2R agent detection...');
  
  // Test cases for R2R agent detection
  const testCases = [
    { engine: 'R2R', expected: true },
    { engine: 'r2r', expected: true },
    { engine: 'R2R_Agent', expected: true },
    { engine: 'openai', expected: false },
    { engine: 'default', expected: false },
    { engine: 'dify', expected: false },
  ];

  let allPassed = true;
  
  for (const testCase of testCases) {
    const result = isR2RAgent(testCase.engine);
    const passed = result === testCase.expected;
    
    console.log(`  Engine: ${testCase.engine} -> ${result} (expected: ${testCase.expected}) ${passed ? '✓' : '✗'}`);
    
    if (!passed) {
      allPassed = false;
    }
  }

  console.log(`R2R agent detection test: ${allPassed ? 'PASSED' : 'FAILED'}`);
  return allPassed;
}

/**
 * Test R2R client service creation
 */
export function testR2RClientServiceCreation(): boolean {
  console.log('Testing R2R client service creation...');
  
  try {
    const clientService = createR2RClientService(defaultChatConfig);
    
    if (!clientService) {
      console.log('  Failed to create R2R client service ✗');
      return false;
    }
    
    console.log('  R2R client service created successfully ✓');
    return true;
  } catch (error) {
    console.log(`  Error creating R2R client service: ${error} ✗`);
    return false;
  }
}

/**
 * Test R2R service creation
 */
export function testR2RServiceCreation(): boolean {
  console.log('Testing R2R service creation...');
  
  try {
    const r2rService = createR2RService({
      chatConfig: defaultChatConfig,
      credentials: {
        email: '<EMAIL>',
        password: 'test',
      },
    });
    
    if (!r2rService) {
      console.log('  Failed to create R2R service ✗');
      return false;
    }
    
    console.log('  R2R service created successfully ✓');
    return true;
  } catch (error) {
    console.log(`  Error creating R2R service: ${error} ✗`);
    return false;
  }
}

/**
 * Test R2R server health check (if server is available)
 */
export async function testR2RServerHealth(): Promise<boolean> {
  console.log('Testing R2R server health...');
  
  try {
    const isHealthy = await checkR2RServerHealth(defaultChatConfig.server.apiUrl);
    
    if (isHealthy) {
      console.log('  R2R server is healthy ✓');
    } else {
      console.log('  R2R server is not healthy (this is expected if server is not running) ⚠');
    }
    
    return true; // Don't fail the test if server is not available
  } catch (error) {
    console.log(`  R2R server health check failed (this is expected if server is not running): ${error} ⚠`);
    return true; // Don't fail the test if server is not available
  }
}

/**
 * Test configuration integration
 */
export function testConfigurationIntegration(): boolean {
  console.log('Testing configuration integration...');
  
  try {
    // Test default configuration
    const config = defaultChatConfig;
    
    if (!config.server.apiUrl) {
      console.log('  Missing server API URL ✗');
      return false;
    }
    
    if (!config.ragGeneration) {
      console.log('  Missing RAG generation config ✗');
      return false;
    }
    
    if (!config.vectorSearch) {
      console.log('  Missing vector search config ✗');
      return false;
    }
    
    console.log('  Configuration integration test passed ✓');
    return true;
  } catch (error) {
    console.log(`  Configuration integration test failed: ${error} ✗`);
    return false;
  }
}

/**
 * Run all R2R integration tests
 */
export async function runR2RIntegrationTests(): Promise<boolean> {
  console.log('=== R2R Integration Tests ===');
  
  const tests = [
    testR2RAgentDetection(),
    testR2RClientServiceCreation(),
    testR2RServiceCreation(),
    await testR2RServerHealth(),
    testConfigurationIntegration(),
  ];
  
  const passedTests = tests.filter(result => result).length;
  const totalTests = tests.length;
  
  console.log(`\n=== Test Results ===`);
  console.log(`Passed: ${passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('All R2R integration tests PASSED ✓');
    return true;
  } else {
    console.log('Some R2R integration tests FAILED ✗');
    return false;
  }
}

/**
 * Test R2R integration in browser console
 * Usage: Open browser console and run: testR2RIntegration()
 */
export async function testR2RIntegration(): Promise<void> {
  try {
    await runR2RIntegrationTests();
  } catch (error) {
    console.error('R2R integration test failed:', error);
  }
}

// Make test function available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testR2RIntegration = testR2RIntegration;
}
