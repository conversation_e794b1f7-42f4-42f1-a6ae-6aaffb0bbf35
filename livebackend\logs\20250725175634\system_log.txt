[INFO]2025-07-25 17:56:35,676 File 'main.py',line 11: [System] Welcome to Awesome digitalHuman System
[INFO]2025-07-25 17:56:35,676 File 'main.py',line 12: [System] Runing config:
COMMON:
  LOG_LEVEL: DEBUG
  NAME: Awesome-Digital-Human
  VERSION: v3.0.0
SERVER:
  AGENTS:
    DEFAULT: Repeater
    SUPPORT_LIST: [CfgNode({'NAME': 'Repeater', 'VERSION': 'v0.0.1', 'DESC': '复读机', 'META': CfgNode({'official': '', 'configuration': '', 'tips': '测试使用', 'fee': ''})}), CfgNode({'NAME': 'OpenAI', 'VERSION': 'v0.0.1', 'DESC': '接入Openai协议的服务', 'META': CfgNode({'official': '', 'configuration': '', 'tips': '兼容所有符合Openai协议的API', 'fee': ''}), 'PARAMETERS': [{'name': 'model', 'description': 'ID of the model to use.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'base_url', 'description': 'The base url for request.', 'type': 'string', 'required': False, 'choices': [], 'default': 'https://api.openai.com/v1'}, {'name': 'api_key', 'description': 'The api key for request.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用', 'fee': ''}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'FastGPT', 'VERSION': 'v0.0.1', 'DESC': '接入FastGPT应用', 'META': CfgNode({'official': 'https://fastgpt.cn', 'configuration': 'FastGPT云服务: https://cloud.fastgpt.cn', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'base_url', 'description': 'FastGPT base url.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'FastGPT API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'uid', 'description': 'FastGPT customUid.', 'type': 'string', 'required': False, 'choices': [], 'default': 'adh'}]}), CfgNode({'NAME': 'R2R', 'VERSION': 'v0.0.1', 'DESC': 'R2R RAG Agent - 检索增强生成智能体', 'META': CfgNode({'official': 'https://r2r-docs.sciphi.ai/', 'configuration': 'https://r2r-docs.sciphi.ai/documentation/configuration/overview', 'tips': '支持RAG检索、多种搜索策略、对话上下文管理', 'fee': ''}), 'PARAMETERS': [{'name': 'base_url', 'description': 'R2R服务器地址', 'type': 'string', 'required': True, 'choices': [], 'default': 'http://localhost:7272'}, {'name': 'api_key', 'description': 'R2R API密钥（如果需要认证）', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'search_limit', 'description': '搜索结果数量限制', 'type': 'int', 'required': False, 'range': [1, 50], 'choices': [], 'default': 10}, {'name': 'use_hybrid_search', 'description': '启用混合搜索', 'type': 'bool', 'required': False, 'range': [], 'choices': [], 'default': True}, {'name': 'use_vector_search', 'description': '启用向量搜索', 'type': 'bool', 'required': False, 'range': [], 'choices': [], 'default': True}, {'name': 'temperature', 'description': '生成温度', 'type': 'float', 'required': False, 'range': [0.0, 2.0], 'choices': [], 'default': 0.1}, {'name': 'max_tokens', 'description': '最大生成token数', 'type': 'int', 'required': False, 'range': [1, 4096], 'choices': [], 'default': 1024}, {'name': 'top_p', 'description': 'Top-p采样参数', 'type': 'float', 'required': False, 'range': [0.0, 1.0], 'choices': [], 'default': 1.0}, {'name': 'top_k', 'description': 'Top-k采样参数', 'type': 'int', 'required': False, 'range': [1, 200], 'choices': [], 'default': 100}]})]
  ENGINES:
    ASR:
      DEFAULT: Tencent-API
      SUPPORT_LIST: [CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用', 'fee': 'free'}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'Tencent-API', 'VERSION': 'v0.0.1', 'DESC': '接入腾讯服务', 'META': CfgNode({'official': '', 'configuration': 'https://console.cloud.tencent.com/asr', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'secret_id', 'description': 'tencent secret_id.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'secret_key', 'description': 'tencent secret_key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'funasrStreaming', 'VERSION': 'v0.0.1', 'DESC': '接入Stream ASR', 'META': CfgNode({'official': 'https://github.com/modelscope/FunASR', 'tips': '支持本地部署的FunAsrStream应用', 'fee': 'free', 'infer_type': 'stream'}), 'PARAMETERS': [{'name': 'api_url', 'description': 'Funasr Streaming API URL', 'type': 'string', 'required': False, 'choices': [], 'default': 'ws://adh-funasr:10095'}, {'name': 'mode', 'description': 'Funasr Streaming mode', 'type': 'string', 'required': False, 'choices': ['2pass'], 'default': '2pass'}]})]
    LLM:
      DEFAULT: None
      SUPPORT_LIST: []
    TTS:
      DEFAULT: Tencent-API
      SUPPORT_LIST: [CfgNode({'NAME': 'EdgeTTS', 'VERSION': 'v0.0.1', 'DESC': '适配EdgeTTS', 'META': CfgNode({'official': 'https://github.com/rany2/edge-tts', 'configuration': '', 'tips': '开源项目可能存在不稳定的情况', 'fee': 'free'}), 'PARAMETERS': [{'name': 'voice', 'description': 'Voice for TTS.', 'type': 'string', 'required': False, 'choices': ['Getting from voice api...'], 'default': 'zh-CN-XiaoxiaoNeural'}, {'name': 'rate', 'description': 'Set rate, default +0%.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}, {'name': 'volume', 'description': 'Set volume, default +0%.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}, {'name': 'pitch', 'description': 'Set pitch, default +0Hz.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}]}), CfgNode({'NAME': 'Tencent-API', 'VERSION': 'v0.0.1', 'DESC': '接入腾讯服务', 'META': CfgNode({'official': '', 'configuration': 'https://console.cloud.tencent.com/tts', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'secret_id', 'description': 'tencent secret_id.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'secret_key', 'description': 'tencent secret_key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'voice', 'description': 'Voice for TTS.', 'type': 'string', 'required': False, 'choices': [502001], 'default': '502001'}, {'name': 'volume', 'description': 'Set volume, default +0%.', 'type': 'float', 'required': False, 'range': [-10, 10], 'default': 5}, {'name': 'speed', 'description': 'Set speed, default +0%.', 'type': 'float', 'required': False, 'range': [-2, 6], 'default': 0.0}]}), CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用全', 'fee': ''}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]})]
  IP: 0.0.0.0
  PORT: 8880
  WORKSPACE_PATH: ./outputs
[INFO]2025-07-25 17:56:35,676 File 'asrFactory.py',line 23: [ASRFactory] Create engine: Dify
[INFO]2025-07-25 17:56:35,676 File 'enginePool.py',line 44: [EnginePool] ASR Engine Dify is created.
[INFO]2025-07-25 17:56:35,676 File 'asrFactory.py',line 23: [ASRFactory] Create engine: Tencent-API
[INFO]2025-07-25 17:56:35,676 File 'enginePool.py',line 44: [EnginePool] ASR Engine Tencent-API is created.
[INFO]2025-07-25 17:56:35,676 File 'asrFactory.py',line 23: [ASRFactory] Create engine: funasrStreaming
[INFO]2025-07-25 17:56:35,688 File 'enginePool.py',line 44: [EnginePool] ASR Engine funasrStreaming is created.
[INFO]2025-07-25 17:56:35,688 File 'enginePool.py',line 45: [EnginePool] ASR Engine default is Tencent-API.
[INFO]2025-07-25 17:56:35,688 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: EdgeTTS
[INFO]2025-07-25 17:56:35,688 File 'enginePool.py',line 49: [EnginePool] TTS Engine EdgeTTS is created.
[INFO]2025-07-25 17:56:35,688 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: Tencent-API
[INFO]2025-07-25 17:56:35,688 File 'enginePool.py',line 49: [EnginePool] TTS Engine Tencent-API is created.
[INFO]2025-07-25 17:56:35,688 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: Dify
[INFO]2025-07-25 17:56:35,688 File 'enginePool.py',line 49: [EnginePool] TTS Engine Dify is created.
[INFO]2025-07-25 17:56:35,688 File 'enginePool.py',line 50: [EnginePool] TTS Engine default is Tencent-API.
[INFO]2025-07-25 17:56:35,688 File 'enginePool.py',line 55: [EnginePool] LLM Engine default is None.
[INFO]2025-07-25 17:56:35,688 File 'agentFactory.py',line 21: [AgentFactory] Create instance: Repeater
[INFO]2025-07-25 17:56:35,688 File 'agentPool.py',line 39: [AgentPool] AGENT Engine Repeater is created.
[INFO]2025-07-25 17:56:35,688 File 'agentFactory.py',line 21: [AgentFactory] Create instance: OpenAI
[INFO]2025-07-25 17:56:35,688 File 'agentPool.py',line 39: [AgentPool] AGENT Engine OpenAI is created.
[INFO]2025-07-25 17:56:35,688 File 'agentFactory.py',line 21: [AgentFactory] Create instance: Dify
[INFO]2025-07-25 17:56:35,688 File 'agentPool.py',line 39: [AgentPool] AGENT Engine Dify is created.
[INFO]2025-07-25 17:56:35,688 File 'agentFactory.py',line 21: [AgentFactory] Create instance: FastGPT
[INFO]2025-07-25 17:56:35,688 File 'agentPool.py',line 39: [AgentPool] AGENT Engine FastGPT is created.
[INFO]2025-07-25 17:56:35,688 File 'agentFactory.py',line 21: [AgentFactory] Create instance: R2R
[INFO]2025-07-25 17:56:35,688 File 'agentPool.py',line 39: [AgentPool] AGENT Engine R2R is created.
[INFO]2025-07-25 17:56:35,688 File 'agentPool.py',line 40: [AgentPool] AGENT Engine default is Repeater.
[INFO]2025-07-25 17:59:24,108 File 'r2rAgent.py',line 84: [R2RAgent] Initializing with base_url: http://192.168.0.115:7272
[INFO]2025-07-25 17:59:24,286 File 'r2rAgent.py',line 102: [R2RAgent] Created new conversation: 007b825d-6673-4615-8430-47e9e21965a9
[INFO]2025-07-25 17:59:24,287 File 'r2rAgent.py',line 121: [R2RAgent] Processing message: 你好...
[INFO]2025-07-25 17:59:24,287 File 'r2rAgent.py',line 131: [R2RAgent] Calling R2R agent with streaming enabled
[INFO]2025-07-25 17:59:24,291 File 'r2rAgent.py',line 147: [R2RAgent] Got stream response: <class 'generator'>
[INFO]2025-07-25 17:59:24,292 File 'r2rAgent.py',line 156: [R2RAgent] Starting to iterate through R2R stream
[DEBUG]2025-07-25 17:59:29,536 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e9599596', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='你好！我', annotations=[]))])))
[DEBUG]2025-07-25 17:59:29,538 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 你好！我...
[DEBUG]2025-07-25 17:59:29,623 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_38f8d8ea', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='是一个AI研', annotations=[]))])))
[DEBUG]2025-07-25 17:59:29,624 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 是一个AI研...
[DEBUG]2025-07-25 17:59:29,661 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fb6b6925', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='究助手，可', annotations=[]))])))
[DEBUG]2025-07-25 17:59:29,662 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 究助手，可...
[DEBUG]2025-07-25 17:59:29,707 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8346d72b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='以帮助您', annotations=[]))])))
[DEBUG]2025-07-25 17:59:29,709 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 以帮助您...
[DEBUG]2025-07-25 17:59:29,812 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c79f73d1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='搜索和分', annotations=[]))])))
[DEBUG]2025-07-25 17:59:29,814 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 搜索和分...
[DEBUG]2025-07-25 17:59:29,880 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5103d33a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='析文档', annotations=[]))])))
[DEBUG]2025-07-25 17:59:29,881 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 析文档...
[DEBUG]2025-07-25 17:59:30,000 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_525706df', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='内容。我看', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,001 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 内容。我看...
[DEBUG]2025-07-25 17:59:30,039 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_21d5bf37', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='到您的', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,041 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 到您的...
[DEBUG]2025-07-25 17:59:30,147 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a9f56004', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='文档库', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,149 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 文档库...
[DEBUG]2025-07-25 17:59:30,183 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_41136086', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='中有一份', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,184 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 中有一份...
[DEBUG]2025-07-25 17:59:30,286 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0c9945d9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='浙江农林大', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,287 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 浙江农林大...
[DEBUG]2025-07-25 17:59:30,328 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_864965f8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='学朋辈', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,330 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 学朋辈...
[DEBUG]2025-07-25 17:59:30,377 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_888d69fd', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='心理互助员', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,378 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 心理互助员...
[DEBUG]2025-07-25 17:59:30,426 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8356c14e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='工作手册。', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,429 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 工作手册。...
[DEBUG]2025-07-25 17:59:30,518 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_06e29260', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='\n\n请问您需', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,518 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 

请问您需...
[DEBUG]2025-07-25 17:59:30,667 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5937948f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='要我帮您', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,669 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 要我帮您...
[DEBUG]2025-07-25 17:59:30,762 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fcd62dca', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='做什么呢？', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,762 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 做什么呢？...
[DEBUG]2025-07-25 17:59:30,859 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_4b16deb0', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='比如：\n- ', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,861 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 比如：
- ...
[DEBUG]2025-07-25 17:59:30,909 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d2df14d0', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='搜索特定', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,910 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 搜索特定...
[DEBUG]2025-07-25 17:59:30,991 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5e6254cd', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='的信息或', annotations=[]))])))
[DEBUG]2025-07-25 17:59:30,992 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 的信息或...
[DEBUG]2025-07-25 17:59:31,087 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_043c5541', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='主题\n- 查', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,088 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 主题
- 查...
[DEBUG]2025-07-25 17:59:31,186 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_466ad3da', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='看文档的具', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,187 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 看文档的具...
[DEBUG]2025-07-25 17:59:31,239 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d1c74870', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='体内容\n-', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,240 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 体内容
-...
[DEBUG]2025-07-25 17:59:31,281 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_1fcd4950', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' 回', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,282 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string:  回...
[DEBUG]2025-07-25 17:59:31,380 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a7f3a8a5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='答关于心', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,381 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 答关于心...
[DEBUG]2025-07-25 17:59:31,431 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5196aad4', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='理互助工', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,432 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 理互助工...
[DEBUG]2025-07-25 17:59:31,480 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_035cd70a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='作的问题', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,481 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 作的问题...
[DEBUG]2025-07-25 17:59:31,592 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_240e9945', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='\n- 或者其他任', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,594 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 
- 或者其他任...
[DEBUG]2025-07-25 17:59:31,612 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9aecec4b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='何您需要', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,613 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 何您需要...
[DEBUG]2025-07-25 17:59:31,841 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d8865b24', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='帮助的事情', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,843 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 帮助的事情...
[DEBUG]2025-07-25 17:59:31,847 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_453b3ec6', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='\n\n请告诉我您', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,848 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 

请告诉我您...
[DEBUG]2025-07-25 17:59:31,901 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_4996e476', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='的具体需求，我', annotations=[]))])))
[DEBUG]2025-07-25 17:59:31,903 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 的具体需求，我...
[DEBUG]2025-07-25 17:59:32,000 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_33f4ff91', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='会尽力为您提', annotations=[]))])))
[DEBUG]2025-07-25 17:59:32,001 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 会尽力为您提...
[DEBUG]2025-07-25 17:59:32,099 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2fb58431', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='供帮助！', annotations=[]))])))
[DEBUG]2025-07-25 17:59:32,100 File 'r2rAgent.py',line 166: [R2RAgent] Converted chunk to string: 供帮助！...
[DEBUG]2025-07-25 17:59:32,139 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: FinalAnswerEvent(event='final_answer', data=FinalAnswerData(generated_answer='你好！我是一个AI研究助手，可以帮助您搜索和分析文档内容。我看到您的文档库中有一份浙江农林大学朋辈心理互助员工作手册。\n\n请问您需要我帮您做什么呢？比如：\n- 搜索特定的信息或主题\n- 查看文档的具体内容\n- 回答关于心理互助工作的问题\n- 或者其他任何您需要帮助的事情\n\n请告诉我您的具体需求，我会尽力为您提供帮助！', citations=[]))
[DEBUG]2025-07-25 17:59:32,141 File 'r2rAgent.py',line 158: [R2RAgent] Received chunk: None
[INFO]2025-07-25 17:59:35,640 File 'r2rAgent.py',line 167: [R2RAgent] Finished iterating, got 34 chunks
[DEBUG]2025-07-25 17:59:35,642 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 你好！我...
[DEBUG]2025-07-25 17:59:35,643 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 是一个AI研...
[DEBUG]2025-07-25 17:59:35,644 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 究助手，可...
[DEBUG]2025-07-25 17:59:35,645 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 以帮助您...
[DEBUG]2025-07-25 17:59:35,646 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 搜索和分...
[DEBUG]2025-07-25 17:59:35,647 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 析文档...
[DEBUG]2025-07-25 17:59:35,648 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 内容。我看...
[DEBUG]2025-07-25 17:59:35,649 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 到您的...
[DEBUG]2025-07-25 17:59:35,650 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 文档库...
[DEBUG]2025-07-25 17:59:35,651 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 中有一份...
[DEBUG]2025-07-25 17:59:35,651 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 浙江农林大...
[DEBUG]2025-07-25 17:59:35,652 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 学朋辈...
[DEBUG]2025-07-25 17:59:35,653 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 心理互助员...
[DEBUG]2025-07-25 17:59:35,653 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 工作手册。...
[DEBUG]2025-07-25 17:59:35,654 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 

请问您需...
[DEBUG]2025-07-25 17:59:35,654 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 要我帮您...
[DEBUG]2025-07-25 17:59:35,655 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 做什么呢？...
[DEBUG]2025-07-25 17:59:35,656 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 比如：
- ...
[DEBUG]2025-07-25 17:59:35,656 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 搜索特定...
[DEBUG]2025-07-25 17:59:35,656 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 的信息或...
[DEBUG]2025-07-25 17:59:35,657 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 主题
- 查...
[DEBUG]2025-07-25 17:59:35,657 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 看文档的具...
[DEBUG]2025-07-25 17:59:35,657 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 体内容
-...
[DEBUG]2025-07-25 17:59:35,658 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk:  回...
[DEBUG]2025-07-25 17:59:35,658 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 答关于心...
[DEBUG]2025-07-25 17:59:35,659 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 理互助工...
[DEBUG]2025-07-25 17:59:35,659 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 作的问题...
[DEBUG]2025-07-25 17:59:35,660 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 
- 或者其他任...
[DEBUG]2025-07-25 17:59:35,660 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 何您需要...
[DEBUG]2025-07-25 17:59:35,661 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 帮助的事情...
[DEBUG]2025-07-25 17:59:35,661 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 

请告诉我您...
[DEBUG]2025-07-25 17:59:35,662 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 的具体需求，我...
[DEBUG]2025-07-25 17:59:35,662 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 会尽力为您提...
[DEBUG]2025-07-25 17:59:35,663 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: 供帮助！...
[INFO]2025-07-25 17:59:35,663 File 'r2rAgent.py',line 198: [R2RAgent] Completed response, total length: 159
[DEBUG]2025-07-25 17:59:35,682 File 'tencentTTS.py',line 185: [TTS] Engine input: 你好！我是一个AI研究助手，可以帮助您搜索和分析文档内容。
[DEBUG]2025-07-25 17:59:36,423 File 'tencentTTS.py',line 185: [TTS] Engine input: 我看到您的文档库中有一份浙江农林大学朋辈心理互助员工作手册。
[DEBUG]2025-07-25 17:59:37,054 File 'tencentTTS.py',line 185: [TTS] Engine input: 请问您需要我帮您做什么呢？
[DEBUG]2025-07-25 17:59:37,408 File 'tencentTTS.py',line 185: [TTS] Engine input: 比如：  搜索特定的信息或主题  查看文档的具体内容  回答关于心理互助工作的问题  或者其他任何您需要帮助的事情  请告诉我您的具体需求，我会尽力为您提供帮助！
[INFO]2025-07-25 18:01:23,139 File 'r2rAgent.py',line 84: [R2RAgent] Initializing with base_url: http://192.168.0.115:7272
[INFO]2025-07-25 18:01:23,299 File 'r2rAgent.py',line 121: [R2RAgent] Processing message: 心里互助员如何倾听？...
[INFO]2025-07-25 18:01:23,300 File 'r2rAgent.py',line 131: [R2RAgent] Calling R2R agent with streaming enabled
[INFO]2025-07-25 18:01:23,301 File 'r2rAgent.py',line 147: [R2RAgent] Got stream response: <class 'generator'>
[INFO]2025-07-25 18:01:23,302 File 'r2rAgent.py',line 156: [R2RAgent] Starting to iterate through R2R stream
[ERROR]2025-07-25 18:01:23,539 File 'r2rAgent.py',line 170: [R2RAgent] Error iterating stream: Attempted to access streaming response content, without having called `read()`.
[DEBUG]2025-07-25 18:01:23,541 File 'r2rAgent.py',line 181: [R2RAgent] Yielded chunk: Error processing R2R response: Attempted to access...
[INFO]2025-07-25 18:01:23,542 File 'r2rAgent.py',line 198: [R2RAgent] Completed response, total length: 110
[DEBUG]2025-07-25 18:01:23,555 File 'tencentTTS.py',line 185: [TTS] Engine input: Error processing R2R response: Attempted to access streaming response content, without having called read() .
[ERROR]2025-07-25 18:02:39,653 File 'openaiAgent.py',line 66: [OpenaiApiAgent] Exception: Request timed out.
Traceback (most recent call last):
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection.py", line 156, in _connect
    stream = await stream.start_tls(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_backends\anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
  File "C:\ProgramData\anaconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\_base_client.py", line 1526, in request
    response = await self._client.send(
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\ProgramData\anaconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\agent\core\openaiAgent.py", line 62, in run
    async for parseResult in resonableStreamingParser(generator(user.user_id, conversation_id, input.data)):
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\utils\streamParser.py", line 15, in resonableStreamingParser
    async for eventType, chunk in generator:
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\agent\core\openaiAgent.py", line 44, in generator
    async for chunk in OpenaiLLM.chat(
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\core\openai.py", line 25, in chat
    completions = await client.chat.completions.create(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\_base_client.py", line 1791, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\_base_client.py", line 1544, in request
    raise APITimeoutError(request=request) from err
openai.APITimeoutError: Request timed out.
[ERROR]2025-07-25 18:03:19,438 File 'openaiAgent.py',line 66: [OpenaiApiAgent] Exception: Request timed out.
Traceback (most recent call last):
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection.py", line 156, in _connect
    stream = await stream.start_tls(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_backends\anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
  File "C:\ProgramData\anaconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\_base_client.py", line 1526, in request
    response = await self._client.send(
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\ProgramData\anaconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\agent\core\openaiAgent.py", line 62, in run
    async for parseResult in resonableStreamingParser(generator(user.user_id, conversation_id, input.data)):
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\utils\streamParser.py", line 15, in resonableStreamingParser
    async for eventType, chunk in generator:
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\agent\core\openaiAgent.py", line 44, in generator
    async for chunk in OpenaiLLM.chat(
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\core\openai.py", line 25, in chat
    completions = await client.chat.completions.create(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\_base_client.py", line 1791, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\_base_client.py", line 1544, in request
    raise APITimeoutError(request=request) from err
openai.APITimeoutError: Request timed out.
