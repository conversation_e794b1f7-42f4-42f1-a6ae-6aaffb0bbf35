<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>useUserContext 修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .container {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .success {
            color: #4ade80;
        }
        .error {
            color: #f87171;
        }
        .warning {
            color: #fbbf24;
        }
        .info {
            color: #60a5fa;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        #results {
            background-color: #1f2937;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>useUserContext 修复测试</h1>
        <p>此页面用于测试 useUserContext hook 的修复是否有效</p>
        
        <div>
            <button onclick="testUserContextAvailability()">测试 UserContext 可用性</button>
            <button onclick="testR2RConfigAccess()">测试 R2R 配置访问</button>
            <button onclick="testAuthStateAccess()">测试认证状态访问</button>
            <button onclick="clearResults()">清除结果</button>
        </div>
    </div>

    <div class="container">
        <h2>测试结果</h2>
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            results.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            results.scrollTop = results.scrollTop;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testUserContextAvailability() {
            addResult('测试 UserContext 可用性...', 'info');
            
            // 检查 localStorage 中的认证信息
            const accessToken = localStorage.getItem('livechatAccessToken');
            const refreshToken = localStorage.getItem('livechatRefreshToken');
            const authState = localStorage.getItem('authState');
            
            if (accessToken) {
                addResult(`✓ 找到访问令牌: ${accessToken.substring(0, 20)}...`, 'success');
            } else {
                addResult('⚠ 未找到访问令牌', 'warning');
            }
            
            if (refreshToken) {
                addResult(`✓ 找到刷新令牌: ${refreshToken.substring(0, 20)}...`, 'success');
            } else {
                addResult('⚠ 未找到刷新令牌', 'warning');
            }
            
            if (authState) {
                try {
                    const auth = JSON.parse(authState);
                    addResult(`✓ 认证状态: ${JSON.stringify(auth, null, 2)}`, 'success');
                } catch (e) {
                    addResult(`✗ 认证状态解析失败: ${e.message}`, 'error');
                }
            } else {
                addResult('⚠ 未找到认证状态', 'warning');
            }
        }

        function testR2RConfigAccess() {
            addResult('测试 R2R 配置访问...', 'info');
            
            // 模拟 R2R 配置访问
            try {
                const mockAgentSettings = {
                    base_url: 'http://192.168.0.115:7272',
                    api_key: '',
                    search_limit: 10,
                    use_hybrid_search: true,
                    use_vector_search: true,
                    temperature: 0.7
                };
                
                addResult(`✓ 模拟 Agent 设置: ${JSON.stringify(mockAgentSettings, null, 2)}`, 'success');
                
                // 模拟凭据获取
                const accessToken = localStorage.getItem('livechatAccessToken');
                const authState = localStorage.getItem('authState');
                
                let email = '<EMAIL>';
                if (authState) {
                    try {
                        const auth = JSON.parse(authState);
                        email = auth.email || email;
                    } catch (e) {
                        // 忽略解析错误
                    }
                }
                
                const mockCredentials = {
                    email: email,
                    accessToken: accessToken || '',
                    apiKey: mockAgentSettings.api_key || '',
                };
                
                addResult(`✓ 模拟 R2R 凭据: ${JSON.stringify(mockCredentials, null, 2)}`, 'success');
                
            } catch (error) {
                addResult(`✗ R2R 配置访问失败: ${error.message}`, 'error');
            }
        }

        function testAuthStateAccess() {
            addResult('测试认证状态访问...', 'info');
            
            try {
                // 模拟安全的认证状态访问
                let authState = { 
                    isAuthenticated: false, 
                    email: null, 
                    userRole: null, 
                    userId: null 
                };
                
                const storedAuthState = localStorage.getItem('authState');
                if (storedAuthState) {
                    try {
                        const parsed = JSON.parse(storedAuthState);
                        authState = { ...authState, ...parsed };
                        addResult(`✓ 成功获取认证状态: ${JSON.stringify(authState, null, 2)}`, 'success');
                    } catch (e) {
                        addResult(`⚠ 认证状态解析失败，使用默认值: ${e.message}`, 'warning');
                    }
                } else {
                    addResult('⚠ 未找到存储的认证状态，使用默认值', 'warning');
                }
                
                addResult(`✓ 最终认证状态: ${JSON.stringify(authState, null, 2)}`, 'success');
                
            } catch (error) {
                addResult(`✗ 认证状态访问失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            addResult('页面加载完成，开始自动测试...', 'info');
            testUserContextAvailability();
            testR2RConfigAccess();
            testAuthStateAccess();
        };
    </script>
</body>
</html>
