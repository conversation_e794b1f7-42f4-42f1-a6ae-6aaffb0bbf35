/**
 * R2R Service Manager
 * Manages R2R client instances and provides integration with the livechat application
 */

import { R2RClientService, createR2RClientService, R2RMessage, R2RAgentParams } from './r2rClient';
import { ChatConfig } from '../types';
import { CHAT_ROLE, EventResponse, STREAMING_EVENT_TYPE } from '../protocol';

export interface R2RServiceConfig {
  chatConfig: ChatConfig;
  credentials?: {
    email: string;
    password: string;
  };
}

/**
 * R2R Service for managing direct R2R API integration
 */
export class R2RService {
  private clientService: R2RClientService;
  private config: R2RServiceConfig;
  private currentConversationId: string | null = null;

  constructor(config: R2RServiceConfig) {
    this.config = config;
    this.clientService = createR2RClientService(config.chatConfig);
  }

  /**
   * Initialize the R2R service
   */
  async initialize(): Promise<void> {
    try {
      await this.clientService.initialize(this.config.credentials);
    } catch (error) {
      console.error('Failed to initialize R2R service:', error);
      throw error;
    }
  }

  /**
   * Check if the service is ready
   */
  isReady(): boolean {
    return this.clientService.isClientAuthenticated();
  }

  /**
   * Start a new conversation
   */
  async startNewConversation(): Promise<string> {
    try {
      this.currentConversationId = await this.clientService.createConversation();
      return this.currentConversationId;
    } catch (error) {
      console.error('Failed to start new conversation:', error);
      throw error;
    }
  }

  /**
   * Get current conversation ID
   */
  getCurrentConversationId(): string | null {
    return this.currentConversationId;
  }

  /**
   * Set current conversation ID
   */
  setCurrentConversationId(conversationId: string | null): void {
    this.currentConversationId = conversationId;
  }

  /**
   * Send a message and handle streaming response
   * This method converts R2R streaming response to the format expected by livechat
   */
  async sendMessageWithStreaming(
    message: string,
    onEvent: (response: EventResponse) => void,
    onError: (error: Error) => void,
    signal?: AbortSignal
  ): Promise<void> {
    try {
      // Create conversation if none exists
      if (!this.currentConversationId) {
        this.currentConversationId = await this.startNewConversation();
        
        // Emit conversation ID event
        onEvent({
          event: STREAMING_EVENT_TYPE.CONVERSATION_ID,
          data: this.currentConversationId,
        });
      }

      // Prepare message in R2R format
      const r2rMessage: R2RMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: message,
        timestamp: Date.now(),
        sources: {},
      };

      // Prepare agent parameters
      const agentParams: R2RAgentParams = {
        message: r2rMessage,
        conversationId: this.currentConversationId,
      };

      // Send message and get streaming response
      const streamResponse = await this.clientService.sendMessage(agentParams);

      // Process streaming response
      await this.processStreamingResponse(streamResponse, onEvent, onError, signal);

    } catch (error) {
      console.error('Failed to send message:', error);
      onError(error as Error);
    }
  }

  /**
   * Process R2R streaming response and convert to livechat events
   */
  private async processStreamingResponse(
    streamResponse: ReadableStream,
    onEvent: (response: EventResponse) => void,
    onError: (error: Error) => void,
    signal?: AbortSignal
  ): Promise<void> {
    try {
      const reader = streamResponse.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        // Check for abort signal
        if (signal?.aborted) {
          reader.releaseLock();
          return;
        }

        const { done, value } = await reader.read();

        if (done) {
          // Emit completion event
          onEvent({
            event: STREAMING_EVENT_TYPE.DONE,
            data: '',
          });
          break;
        }

        // Decode the chunk
        buffer += decoder.decode(value, { stream: true });

        // Process complete lines
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.trim()) {
            try {
              // Try to parse as JSON (R2R might send structured data)
              const data = JSON.parse(line);
              this.handleR2REvent(data, onEvent);
            } catch {
              // If not JSON, treat as text content
              if (line.trim()) {
                onEvent({
                  event: STREAMING_EVENT_TYPE.TEXT,
                  data: line,
                });
              }
            }
          }
        }
      }

      // Process any remaining buffer content
      if (buffer.trim()) {
        try {
          const data = JSON.parse(buffer);
          this.handleR2REvent(data, onEvent);
        } catch {
          onEvent({
            event: STREAMING_EVENT_TYPE.TEXT,
            data: buffer,
          });
        }
      }

    } catch (error) {
      console.error('Error processing streaming response:', error);
      onError(error as Error);
    }
  }

  /**
   * Handle R2R specific events and convert to livechat format
   */
  private handleR2REvent(data: any, onEvent: (response: EventResponse) => void): void {
    // Handle different R2R event types
    if (data.type === 'thinking' || data.event === 'thinking') {
      onEvent({
        event: STREAMING_EVENT_TYPE.THINK,
        data: data.content || data.data || '',
      });
    } else if (data.type === 'message' || data.event === 'message') {
      onEvent({
        event: STREAMING_EVENT_TYPE.TEXT,
        data: data.content || data.data || '',
      });
    } else if (data.type === 'error' || data.event === 'error') {
      onEvent({
        event: STREAMING_EVENT_TYPE.ERROR,
        data: data.message || data.content || data.data || 'Unknown error',
      });
    } else if (data.type === 'completion' || data.event === 'completion') {
      onEvent({
        event: STREAMING_EVENT_TYPE.DONE,
        data: '',
      });
    } else {
      // Default to text content
      const content = data.content || data.data || data.message || '';
      if (content) {
        onEvent({
          event: STREAMING_EVENT_TYPE.TEXT,
          data: content,
        });
      }
    }
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(conversationId?: string) {
    const id = conversationId || this.currentConversationId;
    if (!id) return null;

    return await this.clientService.getConversation(id);
  }

  /**
   * List all conversations
   */
  async listConversations() {
    return await this.clientService.listConversations();
  }

  /**
   * Update configuration
   */
  updateConfig(config: R2RServiceConfig): void {
    this.config = config;
    this.clientService.updateConfig(config.chatConfig);
  }

  /**
   * Clear current conversation
   */
  clearCurrentConversation(): void {
    this.currentConversationId = null;
  }
}

/**
 * Create a new R2R service instance
 */
export const createR2RService = (config: R2RServiceConfig): R2RService => {
  return new R2RService(config);
};
