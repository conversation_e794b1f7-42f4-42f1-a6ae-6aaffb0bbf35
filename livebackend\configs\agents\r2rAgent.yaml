NAME: "R2R"
VERSION: "v0.0.1"
DESC: "R2R RAG Agent - 检索增强生成智能体"
META: {
  official: "https://r2r-docs.sciphi.ai/",
  configuration: "https://r2r-docs.sciphi.ai/documentation/configuration/overview",
  tips: "支持RAG检索、多种搜索策略、对话上下文管理",
  fee: ""
}

# 暴露给前端的参数选项以及默认值
PARAMETERS: [
  {
    name: "base_url",
    description: "R2R服务器地址",
    type: "string",
    required: true,
    choices: [],
    default: "http://localhost:7272"
  },
  {
    name: "api_key",
    description: "R2R API密钥（如果需要认证）",
    type: "string",
    required: false,
    choices: [],
    default: ""
  },

  {
    name: "search_limit",
    description: "搜索结果数量限制",
    type: "int",
    required: false,
    range: [1, 50],
    choices: [],
    default: 10
  },
  {
    name: "use_hybrid_search",
    description: "启用混合搜索",
    type: "bool",
    required: false,
    range: [],
    choices: [],
    default: true
  },
  {
    name: "use_vector_search",
    description: "启用向量搜索",
    type: "bool",
    required: false,
    range: [],
    choices: [],
    default: true
  },
  {
    name: "temperature",
    description: "生成温度",
    type: "float",
    required: false,
    range: [0.0, 2.0],
    choices: [],
    default: 0.1
  },
  {
    name: "max_tokens",
    description: "最大生成token数",
    type: "int",
    required: false,
    range: [1, 4096],
    choices: [],
    default: 1024
  },
  {
    name: "top_p",
    description: "Top-p采样参数",
    type: "float",
    required: false,
    range: [0.0, 1.0],
    choices: [],
    default: 1.0
  },
  {
    name: "top_k",
    description: "Top-k采样参数",
    type: "int",
    required: false,
    range: [1, 200],
    choices: [],
    default: 100
  }
]
