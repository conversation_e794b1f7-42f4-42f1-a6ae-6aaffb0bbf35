[INFO]2025-07-25 18:27:23,432 File 'main.py',line 11: [System] Welcome to Awesome digitalHuman System
[INFO]2025-07-25 18:27:23,432 File 'main.py',line 12: [System] Runing config:
COMMON:
  LOG_LEVEL: DEBUG
  NAME: Awesome-Digital-Human
  VERSION: v3.0.0
SERVER:
  AGENTS:
    DEFAULT: Repeater
    SUPPORT_LIST: [CfgNode({'NAME': 'Repeater', 'VERSION': 'v0.0.1', 'DESC': '复读机', 'META': CfgNode({'official': '', 'configuration': '', 'tips': '测试使用', 'fee': ''})}), CfgNode({'NAME': 'OpenAI', 'VERSION': 'v0.0.1', 'DESC': '接入Openai协议的服务', 'META': CfgNode({'official': '', 'configuration': '', 'tips': '兼容所有符合Openai协议的API', 'fee': ''}), 'PARAMETERS': [{'name': 'model', 'description': 'ID of the model to use.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'base_url', 'description': 'The base url for request.', 'type': 'string', 'required': False, 'choices': [], 'default': 'https://api.openai.com/v1'}, {'name': 'api_key', 'description': 'The api key for request.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用', 'fee': ''}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'FastGPT', 'VERSION': 'v0.0.1', 'DESC': '接入FastGPT应用', 'META': CfgNode({'official': 'https://fastgpt.cn', 'configuration': 'FastGPT云服务: https://cloud.fastgpt.cn', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'base_url', 'description': 'FastGPT base url.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'FastGPT API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'uid', 'description': 'FastGPT customUid.', 'type': 'string', 'required': False, 'choices': [], 'default': 'adh'}]}), CfgNode({'NAME': 'R2R', 'VERSION': 'v0.0.1', 'DESC': 'R2R RAG Agent - 检索增强生成智能体', 'META': CfgNode({'official': 'https://r2r-docs.sciphi.ai/', 'configuration': 'https://r2r-docs.sciphi.ai/documentation/configuration/overview', 'tips': '支持RAG检索、多种搜索策略、对话上下文管理', 'fee': ''}), 'PARAMETERS': [{'name': 'base_url', 'description': 'R2R服务器地址', 'type': 'string', 'required': True, 'choices': [], 'default': 'http://localhost:7272'}, {'name': 'api_key', 'description': 'R2R API密钥（如果需要认证）', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'search_limit', 'description': '搜索结果数量限制', 'type': 'int', 'required': False, 'range': [1, 50], 'choices': [], 'default': 10}, {'name': 'use_hybrid_search', 'description': '启用混合搜索', 'type': 'bool', 'required': False, 'range': [], 'choices': [], 'default': True}, {'name': 'use_vector_search', 'description': '启用向量搜索', 'type': 'bool', 'required': False, 'range': [], 'choices': [], 'default': True}, {'name': 'temperature', 'description': '生成温度', 'type': 'float', 'required': False, 'range': [0.0, 2.0], 'choices': [], 'default': 0.1}, {'name': 'max_tokens', 'description': '最大生成token数', 'type': 'int', 'required': False, 'range': [1, 4096], 'choices': [], 'default': 1024}, {'name': 'top_p', 'description': 'Top-p采样参数', 'type': 'float', 'required': False, 'range': [0.0, 1.0], 'choices': [], 'default': 1.0}, {'name': 'top_k', 'description': 'Top-k采样参数', 'type': 'int', 'required': False, 'range': [1, 200], 'choices': [], 'default': 100}]})]
  ENGINES:
    ASR:
      DEFAULT: Tencent-API
      SUPPORT_LIST: [CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用', 'fee': 'free'}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'Tencent-API', 'VERSION': 'v0.0.1', 'DESC': '接入腾讯服务', 'META': CfgNode({'official': '', 'configuration': 'https://console.cloud.tencent.com/asr', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'secret_id', 'description': 'tencent secret_id.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'secret_key', 'description': 'tencent secret_key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'funasrStreaming', 'VERSION': 'v0.0.1', 'DESC': '接入Stream ASR', 'META': CfgNode({'official': 'https://github.com/modelscope/FunASR', 'tips': '支持本地部署的FunAsrStream应用', 'fee': 'free', 'infer_type': 'stream'}), 'PARAMETERS': [{'name': 'api_url', 'description': 'Funasr Streaming API URL', 'type': 'string', 'required': False, 'choices': [], 'default': 'ws://adh-funasr:10095'}, {'name': 'mode', 'description': 'Funasr Streaming mode', 'type': 'string', 'required': False, 'choices': ['2pass'], 'default': '2pass'}]})]
    LLM:
      DEFAULT: None
      SUPPORT_LIST: []
    TTS:
      DEFAULT: Tencent-API
      SUPPORT_LIST: [CfgNode({'NAME': 'EdgeTTS', 'VERSION': 'v0.0.1', 'DESC': '适配EdgeTTS', 'META': CfgNode({'official': 'https://github.com/rany2/edge-tts', 'configuration': '', 'tips': '开源项目可能存在不稳定的情况', 'fee': 'free'}), 'PARAMETERS': [{'name': 'voice', 'description': 'Voice for TTS.', 'type': 'string', 'required': False, 'choices': ['Getting from voice api...'], 'default': 'zh-CN-XiaoxiaoNeural'}, {'name': 'rate', 'description': 'Set rate, default +0%.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}, {'name': 'volume', 'description': 'Set volume, default +0%.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}, {'name': 'pitch', 'description': 'Set pitch, default +0Hz.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}]}), CfgNode({'NAME': 'Tencent-API', 'VERSION': 'v0.0.1', 'DESC': '接入腾讯服务', 'META': CfgNode({'official': '', 'configuration': 'https://console.cloud.tencent.com/tts', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'secret_id', 'description': 'tencent secret_id.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'secret_key', 'description': 'tencent secret_key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'voice', 'description': 'Voice for TTS.', 'type': 'string', 'required': False, 'choices': [502001], 'default': '502001'}, {'name': 'volume', 'description': 'Set volume, default +0%.', 'type': 'float', 'required': False, 'range': [-10, 10], 'default': 5}, {'name': 'speed', 'description': 'Set speed, default +0%.', 'type': 'float', 'required': False, 'range': [-2, 6], 'default': 0.0}]}), CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用全', 'fee': ''}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]})]
  IP: 0.0.0.0
  PORT: 8880
  WORKSPACE_PATH: ./outputs
[INFO]2025-07-25 18:27:23,432 File 'asrFactory.py',line 23: [ASRFactory] Create engine: Dify
[INFO]2025-07-25 18:27:23,432 File 'enginePool.py',line 44: [EnginePool] ASR Engine Dify is created.
[INFO]2025-07-25 18:27:23,432 File 'asrFactory.py',line 23: [ASRFactory] Create engine: Tencent-API
[INFO]2025-07-25 18:27:23,432 File 'enginePool.py',line 44: [EnginePool] ASR Engine Tencent-API is created.
[INFO]2025-07-25 18:27:23,432 File 'asrFactory.py',line 23: [ASRFactory] Create engine: funasrStreaming
[INFO]2025-07-25 18:27:23,432 File 'enginePool.py',line 44: [EnginePool] ASR Engine funasrStreaming is created.
[INFO]2025-07-25 18:27:23,432 File 'enginePool.py',line 45: [EnginePool] ASR Engine default is Tencent-API.
[INFO]2025-07-25 18:27:23,432 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: EdgeTTS
[INFO]2025-07-25 18:27:23,435 File 'enginePool.py',line 49: [EnginePool] TTS Engine EdgeTTS is created.
[INFO]2025-07-25 18:27:23,435 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: Tencent-API
[INFO]2025-07-25 18:27:23,435 File 'enginePool.py',line 49: [EnginePool] TTS Engine Tencent-API is created.
[INFO]2025-07-25 18:27:23,435 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: Dify
[INFO]2025-07-25 18:27:23,435 File 'enginePool.py',line 49: [EnginePool] TTS Engine Dify is created.
[INFO]2025-07-25 18:27:23,435 File 'enginePool.py',line 50: [EnginePool] TTS Engine default is Tencent-API.
[INFO]2025-07-25 18:27:23,435 File 'enginePool.py',line 55: [EnginePool] LLM Engine default is None.
[INFO]2025-07-25 18:27:23,435 File 'agentFactory.py',line 21: [AgentFactory] Create instance: Repeater
[INFO]2025-07-25 18:27:23,435 File 'agentPool.py',line 39: [AgentPool] AGENT Engine Repeater is created.
[INFO]2025-07-25 18:27:23,435 File 'agentFactory.py',line 21: [AgentFactory] Create instance: OpenAI
[INFO]2025-07-25 18:27:23,437 File 'agentPool.py',line 39: [AgentPool] AGENT Engine OpenAI is created.
[INFO]2025-07-25 18:27:23,437 File 'agentFactory.py',line 21: [AgentFactory] Create instance: Dify
[INFO]2025-07-25 18:27:23,437 File 'agentPool.py',line 39: [AgentPool] AGENT Engine Dify is created.
[INFO]2025-07-25 18:27:23,437 File 'agentFactory.py',line 21: [AgentFactory] Create instance: FastGPT
[INFO]2025-07-25 18:27:23,437 File 'agentPool.py',line 39: [AgentPool] AGENT Engine FastGPT is created.
[INFO]2025-07-25 18:27:23,438 File 'agentFactory.py',line 21: [AgentFactory] Create instance: R2R
[INFO]2025-07-25 18:27:23,438 File 'agentPool.py',line 39: [AgentPool] AGENT Engine R2R is created.
[INFO]2025-07-25 18:27:23,438 File 'agentPool.py',line 40: [AgentPool] AGENT Engine default is Repeater.
[ERROR]2025-07-25 18:30:29,907 File 'openaiAgent.py',line 66: [OpenaiApiAgent] Exception: Request timed out.
Traceback (most recent call last):
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    raise exc
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_async\connection.py", line 124, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_backends\auto.py", line 31, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_backends\anyio.py", line 113, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\ProgramData\anaconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\_base_client.py", line 1526, in request
    response = await self._client.send(
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\ProgramData\anaconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\agent\core\openaiAgent.py", line 62, in run
    async for parseResult in resonableStreamingParser(generator(user.user_id, conversation_id, input.data)):
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\utils\streamParser.py", line 15, in resonableStreamingParser
    async for eventType, chunk in generator:
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\agent\core\openaiAgent.py", line 44, in generator
    async for chunk in OpenaiLLM.chat(
  File "D:\LLM\Learning\ai_coding\anythingchat\livebackend\digitalHuman\core\openai.py", line 25, in chat
    completions = await client.chat.completions.create(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\resources\chat\completions\completions.py", line 2454, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\_base_client.py", line 1791, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\LLM\Learning\ai_coding\anythingchat\.venvlive\Lib\site-packages\openai\_base_client.py", line 1544, in request
    raise APITimeoutError(request=request) from err
openai.APITimeoutError: Request timed out.
[DEBUG]2025-07-25 18:31:59,734 File 'tencentTTS.py',line 185: [TTS] Engine input: Hello, World!   Is there anything specific you d like to explore or create with this classic phrase?
[DEBUG]2025-07-25 18:32:02,670 File 'tencentTTS.py',line 185: [TTS] Engine input: For example:   Code : Print it in a programming language (Python, JavaScript, etc.).   Fun : Generate ASCII art or a creative twist.   Learning : Explain why Hello, World! is iconic in coding.   Let me know how I can help!
