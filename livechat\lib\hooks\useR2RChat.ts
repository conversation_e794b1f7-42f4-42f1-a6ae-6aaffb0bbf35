/**
 * R2R Chat Hook
 * Handles direct R2R API integration for R2R agent mode
 */

import { useEffect, useRef, useState } from "react";
import { 
    useChatRecordStore, 
    useSentioAgentStore, 
    useSentioTtsStore,
    useSentioBasicStore,
} from "@/lib/store/sentio";
import { useTranslations } from 'next-intl';
import { CHAT_ROLE, EventResponse, STREAMING_EVENT_TYPE } from "@/lib/protocol";
import { Live2dManager } from '@/lib/live2d/live2dManager';
import { SENTIO_TTS_PUNC } from '@/lib/constants';
import { base64ToArrayBuffer, ttsTextPreprocess } from '@/lib/func';
import { convertMp3ArrayBufferToWavArrayBuffer } from "@/lib/utils/audio";
import { addToast } from "@heroui/react";
import { SENTIO_TTS_SENTENCE_LENGTH_MIN } from "@/lib/constants";
import { R2RService, createR2RService } from '@/lib/services/r2rService';
import { ChatConfig } from '@/lib/types';
import { api_tts_infer } from '@/lib/api/server';
import { useR2RConfig } from './useConfig';

// Helper function to find punctuation index
const findPuncIndex = (content: string, beginIndex: number) => {
    let latestIndex = -1;
    for (let i = 0; i < SENTIO_TTS_PUNC.length; i++) {
        const index = content.indexOf(SENTIO_TTS_PUNC[i], beginIndex);
        if (index > beginIndex) {
            if (latestIndex < 0 || index < latestIndex) {
                latestIndex = index;
            }
        }
    }
    return latestIndex;
}

/**
 * Hook for R2R direct API integration
 */
export function useR2RChat() {
    const [chatting, setChatting] = useState(false);
    const [r2rService, setR2RService] = useState<R2RService | null>(null);
    const { engine: agentEngine, settings: agentSettings } = useSentioAgentStore();
    const { engine: ttsEngine, settings: ttsSettings } = useSentioTtsStore();
    const { sound } = useSentioBasicStore();
    const { config: r2rConfig, credentials: r2rCredentials } = useR2RConfig();

    const { addChatRecord, updateLastRecord } = useChatRecordStore();
    const controller = useRef<AbortController | null>(null);
    const conversationId = useRef<string>("");
    const messageId = useRef<string>("");

    // Initialize R2R service
    useEffect(() => {
        const initializeR2RService = async () => {
            try {
                const service = createR2RService({
                    chatConfig: r2rConfig,
                    credentials: r2rCredentials,
                });

                await service.initialize();
                setR2RService(service);
            } catch (error) {
                console.error('Failed to initialize R2R service:', error);
                addToast({
                    title: 'Failed to initialize R2R service',
                    color: "danger",
                });
            }
        };

        // Only initialize if agent engine is R2R
        if (isR2RAgent(agentEngine)) {
            initializeR2RService();
        } else {
            setR2RService(null);
        }
    }, [agentEngine, agentSettings, r2rConfig, r2rCredentials]);

    const abort = () => {
        setChatting(false);
        Live2dManager.getInstance().stopAudio();
        if (controller.current) {
            controller.current.abort("abort");
            controller.current = null;
        }
    }

    const chatWithR2RAgent = (
        message: string, 
        postProcess?: (conversation_id: string, message_id: string, think: string, content: string) => void
    ) => {
        if (!r2rService || !r2rService.isReady()) {
            addToast({
                title: 'R2R service not ready',
                color: "danger",
            });
            return;
        }

        addChatRecord({ role: CHAT_ROLE.HUMAN, think: "", content: message });
        addChatRecord({ role: CHAT_ROLE.AI, think: "", content: "..." });
        controller.current = new AbortController();
        setChatting(true);
        
        let agentResponse = "";
        let agentThink = "";
        let ttsProcessIndex = 0;
        let agentDone = true;

        const doTTS = () => {
            if (!!!controller.current) return;
            if (!agentDone || agentResponse.length > ttsProcessIndex) {
                let ttsText = "";
                const ttsCallback = (ttsResult: string) => {
                    if (ttsResult != "") {
                        const audioData = base64ToArrayBuffer(ttsResult);
                        convertMp3ArrayBufferToWavArrayBuffer(audioData).then((buffer) => {
                            Live2dManager.getInstance().pushAudioQueue(buffer);
                            ttsText = "";  
                        })
                    }
                    doTTS();
                }

                let beginIndex = ttsProcessIndex;
                while (beginIndex >= ttsProcessIndex) {
                    const puncIndex = findPuncIndex(agentResponse, beginIndex);
                    if (puncIndex > beginIndex) {
                        if (puncIndex - ttsProcessIndex > SENTIO_TTS_SENTENCE_LENGTH_MIN) {
                            ttsText = agentResponse.substring(ttsProcessIndex, puncIndex + 1);
                            ttsProcessIndex = puncIndex + 1;
                            break;
                        } else {
                            beginIndex = puncIndex + 1;
                            continue;
                        }
                    }
                    beginIndex = -1;
                }
                
                if (ttsText.length == 0 && agentDone) {
                    ttsText = agentResponse.substring(ttsProcessIndex);
                    ttsProcessIndex = agentResponse.length;
                }
                
                if (ttsText != "") {
                    const processText = ttsTextPreprocess(ttsText);
                    if (!!processText) {
                        api_tts_infer(
                            ttsEngine, 
                            ttsSettings, 
                            processText, 
                            controller.current?.signal
                        ).then((ttsResult) => {ttsCallback(ttsResult)});
                    } else {
                        ttsCallback("");
                    }
                } else {
                    setTimeout(() => {
                        doTTS();
                    }, 10);
                }
            } else {
                setChatting(false);
            }
        }

        const r2rCallback = (response: EventResponse) => {
            const event = response.event;
            const data = response.data;
            
            switch (event) {
                case STREAMING_EVENT_TYPE.CONVERSATION_ID:
                    conversationId.current = data;
                    break;
                case STREAMING_EVENT_TYPE.MESSAGE_ID:
                    messageId.current = data;
                    break;
                case STREAMING_EVENT_TYPE.THINK:
                    agentThink += data;
                    updateLastRecord({ role: CHAT_ROLE.AI, think: agentThink, content: agentResponse });
                    break;
                case STREAMING_EVENT_TYPE.TEXT:
                    agentResponse += data;
                    updateLastRecord({ role: CHAT_ROLE.AI, think: agentThink, content: agentResponse });
                    if (agentDone) {
                        agentDone = false;
                        if (sound) {
                            doTTS();
                        }
                    }
                    break;
                case STREAMING_EVENT_TYPE.ERROR:
                    addToast({
                        title: data,
                        color: "danger",
                    });
                    break;
                case STREAMING_EVENT_TYPE.TASK:
                case STREAMING_EVENT_TYPE.DONE:
                    if (postProcess) {
                        postProcess(conversationId.current, messageId.current, agentThink, agentResponse);
                    }
                    if (agentDone) {
                        setChatting(false);
                    } else {
                        agentDone = true;
                    }
                    break;
                default:
                    break;
            }
        }

        const r2rErrorCallback = (error: Error) => {
            agentDone = true;
            setChatting(false);
            addToast({
                title: `R2R Error: ${error.message}`,
                color: "danger",
            });
        }

        // Use R2R service for direct API communication
        r2rService.sendMessageWithStreaming(
            message,
            r2rCallback,
            r2rErrorCallback,
            controller.current.signal
        );
    }

    const chat = (
        message: string,
        postProcess?: (conversation_id: string, message_id: string, think: string, content: string) => void
    ) => {
        abort();
        chatWithR2RAgent(message, postProcess);
    }

    useEffect(() => {
        conversationId.current = "";
        return () => {
            abort();
        }
    }, [agentEngine, agentSettings])

    return { 
        chat, 
        abort, 
        chatting, 
        conversationId: conversationId.current,
        isR2RMode: isR2RAgent(agentEngine),
        r2rService 
    };
}

/**
 * Check if the current agent engine is R2R
 */
export function isR2RAgent(agentEngine: string): boolean {
    // Check if the agent engine name indicates R2R
    return agentEngine.toLowerCase().includes('r2r') || 
           agentEngine.toLowerCase() === 'r2r' ||
           agentEngine === 'R2R';
}
