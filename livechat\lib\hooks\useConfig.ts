/**
 * Configuration Hook
 * Manages chat configuration and provides access to R2R settings
 */

import { useState, useEffect } from 'react';
import { ChatConfig } from '../types';
import { defaultChatConfig, loadChatConfig } from '../config/chatConfig';
import { useSentioAgentStore } from '../store/sentio';

/**
 * Hook for managing chat configuration
 */
export function useConfig() {
  const [config, setConfig] = useState<ChatConfig>(defaultChatConfig);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { settings: agentSettings } = useSentioAgentStore();

  // Load configuration on mount
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const loadedConfig = await loadChatConfig();
        setConfig(loadedConfig);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load configuration');
        console.error('Failed to load configuration:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadConfig();
  }, []);

  // Update configuration with agent settings
  const getR2RConfig = (): ChatConfig => {
    return {
      ...config,
      server: {
        ...config.server,
        // Override with agent settings if available
        apiUrl: agentSettings.base_url || config.server.apiUrl,
      },
      vectorSearch: {
        ...config.vectorSearch,
        searchLimit: agentSettings.search_limit || config.vectorSearch.searchLimit,
        enabled: agentSettings.use_vector_search !== undefined 
          ? agentSettings.use_vector_search 
          : config.vectorSearch.enabled,
      },
      hybridSearch: {
        ...config.hybridSearch,
        enabled: agentSettings.use_hybrid_search !== undefined 
          ? agentSettings.use_hybrid_search 
          : config.hybridSearch.enabled,
      },
      ragGeneration: {
        ...config.ragGeneration,
        temperature: agentSettings.temperature !== undefined 
          ? agentSettings.temperature 
          : config.ragGeneration.temperature,
      },
    };
  };

  // Get R2R credentials from agent settings
  const getR2RCredentials = () => {
    return {
      email: agentSettings.email || '<EMAIL>',
      password: agentSettings.password || 'admin',
      apiKey: agentSettings.api_key || '',
    };
  };

  // Update configuration
  const updateConfig = (newConfig: Partial<ChatConfig>) => {
    setConfig(prev => ({
      ...prev,
      ...newConfig,
      server: { ...prev.server, ...newConfig.server },
      app: { ...prev.app, ...newConfig.app },
      vectorSearch: { ...prev.vectorSearch, ...newConfig.vectorSearch },
      hybridSearch: { ...prev.hybridSearch, ...newConfig.hybridSearch },
      graphSearch: { ...prev.graphSearch, ...newConfig.graphSearch },
      ragGeneration: { ...prev.ragGeneration, ...newConfig.ragGeneration },
    }));
  };

  return {
    config,
    r2rConfig: getR2RConfig(),
    r2rCredentials: getR2RCredentials(),
    isLoading,
    error,
    updateConfig,
  };
}

/**
 * Hook for R2R-specific configuration
 */
export function useR2RConfig() {
  const { r2rConfig, r2rCredentials, isLoading, error } = useConfig();
  
  return {
    config: r2rConfig,
    credentials: r2rCredentials,
    isLoading,
    error,
  };
}
